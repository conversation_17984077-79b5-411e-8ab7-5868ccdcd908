import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip,
  RadioGroup,
  Radio
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  LocalOffer as TagIcon,
  Label as LabelIcon,
  Assignment as RequirementIcon,
  Folder as FeatureIcon,
  Description as DocumentIcon
} from '@mui/icons-material';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import html2pdf from 'html2pdf.js';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import Breadcrumbs from '../common/Breadcrumbs';

const DocumentGenerator = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { axios: authAxios, isAuthenticated, loading: authLoading } = useAuth();

  // State management
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [generating, setGenerating] = useState(false);

  // Document state
  const [documentType, setDocumentType] = useState('release_notes');
  const [documentContent, setDocumentContent] = useState('');
  const [isHistoricalView, setIsHistoricalView] = useState(false);
  const [historicalDocumentInfo, setHistoricalDocumentInfo] = useState(null);

  // Filter state
  const [filters, setFilters] = useState({
    tags: [],
    labels: [],
    states: [],
    includeFeatures: true,
    includeRequirements: true,
    groupByFeature: true,
    versionFilter: 'latest', // 'latest' or 'latest_with_state'
    targetState: '' // Used when versionFilter is 'latest_with_state'
  });
  
  // Available options
  const [availableTags, setAvailableTags] = useState([]);
  const [availableLabels, setAvailableLabels] = useState([]);
  const [availableStates, setAvailableStates] = useState([
    // Feature states
    'NEW', 'OPEN', 'CLOSED',
    // Requirement states
    'New', 'Being Drafted', 'Requiring Approval', 'Ready for Dev',
    'Being Built', 'Ready for Test', 'Under Test', 'Testing Satisfactory',
    'Shipped/Deployed to Customer'
  ]);
  
  // Release notes data
  const [releaseData, setReleaseData] = useState(null);
  const [releaseNotes, setReleaseNotes] = useState('');
  const [releaseTitle, setReleaseTitle] = useState('');
  const [savedReleaseNotes, setSavedReleaseNotes] = useState([]);

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    fetchProjectData();

    // Check for historical document recreation
    const urlParams = new URLSearchParams(location.search);
    const recreateId = urlParams.get('recreate');

    if (recreateId) {
      recreateHistoricalDocument(recreateId);
    }
  }, [projectId, authAxios, isAuthenticated, authLoading, navigate, location.search]);

  const recreateHistoricalDocument = async (documentId) => {
    try {
      setLoading(true);
      setError('');

      const response = await authAxios.get(`/api/documents/${documentId}/recreate`);

      // Set the historical document data
      setDocumentContent(convertTextToHtml(response.data.releaseNotes));
      setReleaseNotes(response.data.releaseNotes);
      setReleaseTitle(response.data.documentInfo.title);
      setFilters(response.data.originalParameters);
      setDocumentType(response.data.documentInfo.documentType);
      setIsHistoricalView(true);
      setHistoricalDocumentInfo(response.data.documentInfo);

    } catch (err) {
      console.error('Error recreating historical document:', err);
      setError(err.response?.data?.message || 'Failed to recreate historical document');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Fetch project details
      const projectRes = await authAxios.get(`/api/projects/${projectId}`);
      setProject(projectRes.data);
      
      // Fetch available tags and labels for this project
      const documentDataRes = await authAxios.get(`/api/documents/projects/${projectId}/document-data`);
      setAvailableTags(documentDataRes.data.tags);
      setAvailableLabels(documentDataRes.data.labels);
      setAvailableStates(documentDataRes.data.states || []);

      // Fetch saved documents (both release notes and SRDs)
      const savedDocsRes = await authAxios.get(`/api/documents/projects/${projectId}/documents`);
      setSavedReleaseNotes(savedDocsRes.data);
      
    } catch (err) {
      console.error('Error fetching project data:', err);
      setError(err.response?.data?.message || 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const addFilterItem = (filterType, item) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: [...prev[filterType], item]
    }));
  };

  const removeFilterItem = (filterType, item) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: prev[filterType].filter(i => i !== item)
    }));
  };

  const generateDocument = async () => {
    try {
      setGenerating(true);
      setError('');

      const response = await authAxios.post(`/api/documents/projects/${projectId}/documents`, {
        filters,
        title: releaseTitle || `${documentType === 'srd' ? 'SRD' : 'Release Notes'} - ${new Date().toLocaleDateString()}`,
        documentType
      });

      setReleaseData(response.data.data);
      // Convert plain text to HTML for Quill editor
      const htmlContent = convertTextToHtml(response.data.releaseNotes);
      setDocumentContent(htmlContent);
      setReleaseNotes(response.data.releaseNotes); // Keep for backward compatibility

    } catch (err) {
      console.error('Error generating document:', err);
      setError(err.response?.data?.message || 'Failed to generate document');
    } finally {
      setGenerating(false);
    }
  };

  // Helper function to convert plain text to HTML
  const convertTextToHtml = (text) => {
    if (!text) return '';

    return text
      .split('\n')
      .map(line => {
        // Convert headers
        if (line.match(/^=+$/)) return ''; // Remove underlines
        if (line.trim() && !line.startsWith(' ') && !line.startsWith('-')) {
          // Check if next line would be underlines (header pattern)
          return `<h2>${line.trim()}</h2>`;
        }
        // Convert bullet points
        if (line.startsWith('- ')) {
          return `<p>${line.substring(2)}</p>`;
        }
        // Regular paragraphs
        if (line.trim()) {
          return `<p>${line.trim()}</p>`;
        }
        return '<br>';
      })
      .join('');
  };

  const saveReleaseNotes = async () => {
    try {
      setError('');
      
      const response = await authAxios.post(`/api/release-notes/projects/${projectId}/release-notes`, {
        filters,
        title: releaseTitle || `Release Notes - ${new Date().toLocaleDateString()}`,
        content: releaseNotes,
        save: true
      });
      
      // Refresh saved release notes list
      await fetchProjectData();
      
      setError(''); // Clear any previous errors
      // Could add a success message here
      
    } catch (err) {
      console.error('Error saving release notes:', err);
      setError(err.response?.data?.message || 'Failed to save release notes');
    }
  };

  const saveDocument = async () => {
    try {
      setError('');

      await authAxios.post(`/api/documents/projects/${projectId}/documents`, {
        filters,
        title: releaseTitle || `${documentType === 'srd' ? 'SRD' : 'Release Notes'} - ${new Date().toLocaleDateString()}`,
        content: documentContent || releaseNotes,
        documentType,
        save: true
      });

      // Refresh saved documents list
      await fetchProjectData();

      setError(''); // Clear any previous errors

    } catch (err) {
      console.error('Error saving document:', err);
      setError(err.response?.data?.message || 'Failed to save document');
    }
  };

  const downloadDocument = (format = 'html') => {
    const content = documentContent || releaseNotes;
    const projectName = project?.name || 'Unknown Project';
    const sanitizedProjectName = projectName.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, ' ').trim();
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const docType = documentType === 'srd' ? 'SRD' : 'ReleaseNotes';
    const filename = `${sanitizedProjectName}-${docType}-${currentDate}`;

    if (format === 'html') {
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${releaseTitle || `${docType} - ${projectName}`}</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1, h2, h3 { color: #333; }
            p { line-height: 1.6; }
          </style>
        </head>
        <body>
          ${content}
        </body>
        </html>
      `;

      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (format === 'pdf') {
      const element = document.createElement('div');
      element.innerHTML = content;

      const opt = {
        margin: 1,
        filename: `${filename}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
      };

      html2pdf().set(opt).from(element).save();
    }
  };

  const downloadReleaseNotes = (format = 'txt') => {
    const content = releaseNotes;

    // Create filename in format: "Project Name-ReleaseNotes-Date.txt"
    const projectName = project?.name || 'Unknown Project';
    const sanitizedProjectName = projectName.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, ' ').trim();
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const filename = `${sanitizedProjectName}-ReleaseNotes-${currentDate}.${format}`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (authLoading || loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !project) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <Breadcrumbs
        items={[
          { label: 'Projects', path: '/projects' },
          { label: project?.name || 'Project', path: `/project/${projectId}` },
          { label: 'Documents', path: `/project/${projectId}/documents` }
        ]}
      />

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <DocumentIcon sx={{ mr: 2, color: 'primary.main' }} />
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Document Generator
        </Typography>
        {isHistoricalView && (
          <Chip
            label="Historical View"
            color="info"
            size="small"
            sx={{ ml: 2 }}
          />
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Filter Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 'fit-content' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <FilterIcon sx={{ mr: 1 }} />
              <Typography variant="h6">Filters</Typography>
            </Box>
            
            {/* Document Type Selection */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Document Type
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <RadioGroup
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                row
              >
                <FormControlLabel
                  value="release_notes"
                  control={<Radio />}
                  label="Release Notes"
                />
                <FormControlLabel
                  value="srd"
                  control={<Radio />}
                  label="SRD"
                />
              </RadioGroup>
            </FormControl>

            {/* Document Title */}
            <TextField
              fullWidth
              label={documentType === 'srd' ? 'Document Title' : 'Release Title'}
              value={releaseTitle}
              onChange={(e) => setReleaseTitle(e.target.value)}
              placeholder={documentType === 'srd' ? 'e.g., Software Requirements Document v1.0' : 'e.g., Version 1.0.0, Sprint 15 Release'}
              sx={{ mb: 2 }}
            />

            {/* Version Filter Selection */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Version Selection
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <RadioGroup
                value={filters.versionFilter}
                onChange={(e) => handleFilterChange('versionFilter', e.target.value)}
              >
                <FormControlLabel
                  value="latest"
                  control={<Radio />}
                  label="Always use latest version"
                />
                <FormControlLabel
                  value="latest_with_state"
                  control={<Radio />}
                  label="Use latest version with specific state"
                />
              </RadioGroup>
            </FormControl>

            {/* Target State Selection (only when latest_with_state is selected) */}
            {filters.versionFilter === 'latest_with_state' && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Target State</InputLabel>
                <Select
                  value={filters.targetState}
                  onChange={(e) => handleFilterChange('targetState', e.target.value)}
                  label="Target State"
                >
                  {availableStates.map((state) => (
                    <MenuItem key={state} value={state}>
                      {state}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            {/* Content Type Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Include Content
            </Typography>
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.includeFeatures}
                  onChange={(e) => handleFilterChange('includeFeatures', e.target.checked)}
                />
              }
              label="Features"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.includeRequirements}
                  onChange={(e) => handleFilterChange('includeRequirements', e.target.checked)}
                />
              }
              label="Requirements"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={filters.groupByFeature}
                  onChange={(e) => handleFilterChange('groupByFeature', e.target.checked)}
                />
              }
              label="Group by Feature"
            />

            <Divider sx={{ my: 2 }} />

            {/* Tag Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              <TagIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Release Tags
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Tags</InputLabel>
              <Select
                multiple
                value={filters.tags}
                onChange={(e) => handleFilterChange('tags', e.target.value)}
                label="Select Tags"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableTags.map((tag) => (
                  <MenuItem key={tag} value={tag}>
                    {tag}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Label Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              <LabelIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Labels
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select Labels</InputLabel>
              <Select
                multiple
                value={filters.labels}
                onChange={(e) => handleFilterChange('labels', e.target.value)}
                label="Select Labels"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((label) => (
                      <Chip key={label._id} label={label.name} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableLabels.map((label) => (
                  <MenuItem key={label._id} value={label}>
                    {label.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* State Filters */}
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              States
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Select States</InputLabel>
              <Select
                multiple
                value={filters.states}
                onChange={(e) => handleFilterChange('states', e.target.value)}
                label="Select States"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableStates.map((state) => (
                  <MenuItem key={state} value={state}>
                    {state}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Divider sx={{ my: 2 }} />

            {/* Filtering Logic Info */}
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontStyle: 'italic' }}>
              Note: All selected filters (tags, labels, states) must be met for an item to appear in the release notes (AND logic).
            </Typography>

            {/* Generate Button */}
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={generateDocument}
              disabled={
                generating ||
                (!filters.includeFeatures && !filters.includeRequirements) ||
                !releaseTitle.trim()
              }
              startIcon={generating ? <CircularProgress size={20} /> : <PreviewIcon />}
              sx={{ mb: 2 }}
            >
              {generating ? 'Generating...' : `Generate ${documentType === 'srd' ? 'SRD' : 'Release Notes'}`}
            </Button>
          </Paper>
        </Grid>

        {/* Main Content Area */}
        <Grid item xs={12} md={8}>
          {/* Document Content */}
          <Paper sx={{ p: 3, minHeight: 400, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              {isHistoricalView ? 'Historical Document' : 'Generated Document'}
            </Typography>
            {documentContent || releaseNotes ? (
              <Box>
                {/* Rich Text Editor */}
                <Box sx={{ mb: 2, '& .ql-editor': { minHeight: '400px' } }}>
                  <ReactQuill
                    theme="snow"
                    value={documentContent || releaseNotes}
                    onChange={setDocumentContent}
                    modules={{
                      toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline'],
                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                        ['link'],
                        ['clean']
                      ]
                    }}
                    readOnly={isHistoricalView}
                  />
                </Box>

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {!isHistoricalView && (
                    <Button
                      variant="contained"
                      startIcon={<SaveIcon />}
                      onClick={saveDocument}
                      disabled={!releaseTitle.trim()}
                    >
                      Save
                    </Button>
                  )}
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() => downloadDocument('html')}
                  >
                    Download HTML
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={() => downloadDocument('pdf')}
                  >
                    Download PDF
                  </Button>
                </Box>

                {/* Historical Document Info */}
                {isHistoricalView && historicalDocumentInfo && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    Historical document from {new Date(historicalDocumentInfo.generatedAt).toLocaleString()}
                    {historicalDocumentInfo.documentType && ` (${historicalDocumentInfo.documentType.toUpperCase()})`}
                  </Alert>
                )}
              </Box>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                Configure your filters and click "Generate {documentType === 'srd' ? 'SRD' : 'Release Notes'}" to create your document.
              </Typography>
            )}
          </Paper>

          {/* Saved Documents History */}
          {savedReleaseNotes.length > 0 && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Saved Documents
              </Typography>
              <List>
                {savedReleaseNotes.map((notes) => (
                  <ListItem
                    key={notes._id}
                    sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">{notes.title}</Typography>
                          <Chip
                            label={notes.documentType === 'srd' ? 'SRD' : 'Release Notes'}
                            size="small"
                            color={notes.documentType === 'srd' ? 'primary' : 'secondary'}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Created: {new Date(notes.createdAt).toLocaleString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Filters: {notes.filters.tags?.length || 0} tags, {notes.filters.labels?.length || 0} labels, {notes.filters.states?.length || 0} states
                          </Typography>
                        </Box>
                      }
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="View Document">
                        <IconButton
                          size="small"
                          onClick={() => {
                            // Restore document content with proper formatting
                            const htmlContent = convertTextToHtml(notes.content);
                            setDocumentContent(htmlContent);
                            setReleaseNotes(notes.content);
                            setReleaseTitle(notes.title);
                            setFilters(notes.filters);
                            // Restore document type if available
                            if (notes.documentType) {
                              setDocumentType(notes.documentType);
                            }
                          }}
                        >
                          <PreviewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download">
                        <IconButton
                          size="small"
                          onClick={() => {
                            // Create filename based on document type
                            const projectName = project?.name || 'Unknown Project';
                            const sanitizedProjectName = projectName.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, ' ').trim();
                            const noteDate = new Date(notes.generatedAt).toISOString().split('T')[0]; // YYYY-MM-DD format
                            const docType = notes.documentType === 'srd' ? 'SRD' : 'ReleaseNotes';
                            const filename = `${sanitizedProjectName}-${docType}-${noteDate}.txt`;

                            const blob = new Blob([notes.content], { type: 'text/plain' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = filename;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                          }}
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                ))}
              </List>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default DocumentGenerator;
