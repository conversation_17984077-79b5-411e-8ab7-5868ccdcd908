import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Description as DocumentIcon,
  History as HistoryIcon
} from '@mui/icons-material';

const DocumentLabels = ({ element, elementType, projectId, isCollapsible = true }) => {
  const navigate = useNavigate();

  const handleLabelClick = (label) => {
    // Navigate to Document Generator with historical document recreation
    navigate(`/project/${projectId}/documents?recreate=${label.documentId}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDocumentTypeLabel = (type) => {
    switch (type) {
      case 'srd':
        return 'SRD';
      case 'release_notes':
        return 'Release Notes';
      default:
        return 'Document';
    }
  };

  const getDocumentTypeColor = (type) => {
    switch (type) {
      case 'srd':
        return 'primary';
      case 'release_notes':
        return 'secondary';
      default:
        return 'default';
    }
  };

  // Extract document labels from all versions
  const allLabels = [];
  if (element && element.versions) {
    element.versions.forEach(version => {
      if (version.documentLabels && version.documentLabels.length > 0) {
        version.documentLabels.forEach(label => {
          allLabels.push({
            ...label,
            elementVersion: version.version
          });
        });
      }
    });
  }

  if (!allLabels || allLabels.length === 0) {
    return null;
  }

  // Group labels by documentId to avoid duplicates
  const uniqueDocuments = allLabels.reduce((acc, label) => {
    if (!acc[label.documentId]) {
      acc[label.documentId] = label;
    }
    return acc;
  }, {});

  const uniqueLabels = Object.values(uniqueDocuments);

  const content = (
    <Box>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        {uniqueLabels.map((label) => (
          <Tooltip
            key={label.documentId}
            title={
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {label.title}
                </Typography>
                <Typography variant="caption" display="block">
                  Generated: {formatDate(label.generatedAt)}
                </Typography>
                <Typography variant="caption" display="block">
                  Version: {label.elementVersion}
                </Typography>
                <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                  Click to view historical document
                </Typography>
              </Box>
            }
            arrow
            placement="top"
          >
            <Chip
              icon={<DocumentIcon sx={{ fontSize: 16 }} />}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Typography variant="caption" sx={{ fontWeight: 500 }}>
                    {getDocumentTypeLabel(label.documentType)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    v{label.elementVersion}
                  </Typography>
                </Box>
              }
              onClick={() => handleLabelClick(label)}
              variant="outlined"
              color={getDocumentTypeColor(label.documentType)}
              size="small"
              sx={{
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'action.hover'
                }
              }}
            />
          </Tooltip>
        ))}
      </Box>

      {uniqueLabels.length > 0 && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          This {elementType} has appeared in {uniqueLabels.length} document{uniqueLabels.length !== 1 ? 's' : ''}
        </Typography>
      )}
    </Box>
  );

  if (!isCollapsible) {
    return (
      <Box sx={{ mt: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <HistoryIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            Document History
          </Typography>
        </Box>
        {content}
      </Box>
    );
  }

  return (
    <Paper sx={{ mt: 2 }} elevation={1}>
      <Accordion defaultExpanded={false}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            minHeight: 48,
            '&.Mui-expanded': {
              minHeight: 48
            },
            '& .MuiAccordionSummary-content': {
              margin: '8px 0',
              '&.Mui-expanded': {
                margin: '8px 0'
              }
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <HistoryIcon sx={{ fontSize: 20, mr: 1, color: 'text.secondary' }} />
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              Document History
            </Typography>
            <Chip
              label={uniqueLabels.length}
              size="small"
              color="primary"
              sx={{ ml: 1, height: 20, fontSize: '0.75rem' }}
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ pt: 0 }}>
          {content}
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default DocumentLabels;
