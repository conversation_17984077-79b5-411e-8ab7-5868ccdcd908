const mongoose = require('mongoose');

const versionSchema = new mongoose.Schema({
  version: {
    type: Number,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  state: {
    type: String,
    enum: ['New', 'Being Defined', 'Approved', 'Implementing', 'Done Implementing', 'Testing', 'Done Testing', 'Deployed to Customer'],
    default: 'New'
  },
  labels: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Label'
  }],
  // Release tags for this specific version
  releaseTags: [{
    tag: {
      type: String,
      required: true
    },
    dateAdded: {
      type: Date,
      default: Date.now
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    _id: false // Prevent mongoose from adding _id to subdocs
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  changes: {
    type: [{
      field: String,
      from: String,
      to: String
    }],
    default: []
  },
  comments: [{
    text: {
      type: String,
      required: true
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    version: {
      type: Number,
      required: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  approvals: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Document labels tracking which documents this version has appeared in
  documentLabels: [{
    documentId: {
      type: String,
      required: true
    },
    documentType: {
      type: String,
      enum: ['release_notes', 'srd'],
      required: true
    },
    generatedAt: {
      type: Date,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    _id: false // Prevent mongoose from adding _id to subdocs
  }]
});

const featureSchema = new mongoose.Schema({
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  },
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  // Feature-level state (separate from version state)
  state: {
    type: String,
    enum: ['NEW', 'OPEN', 'CLOSED'],
    default: 'NEW'
  },
  versions: [versionSchema],
  currentVersion: {
    type: Number,
    default: 1
  },

  members: {
    type: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      roles: [{
        type: String,
        enum: ['developer', 'product_manager', 'project_manager', 'tester'],
        required: false
      }]
    }],
    default: [] // Make sure this line exists and sets default to empty array
  },





  //members: [{
    //user: {
      //type: mongoose.Schema.Types.ObjectId,
      //ref: 'User',
      //required: true
    //},
    //roles: [{
     // type: String,
     // enum: ['developer', 'product_manager', 'project_manager', 'tester'],
     // required: true
    //}]
  //}],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Method to add a new version
featureSchema.methods.addVersion = async function(versionData) {
  this.currentVersion += 1;
  versionData.version = this.currentVersion;
  this.versions.push(versionData);
  return this.save();
};

// Method to get a specific version
featureSchema.methods.getVersion = function(versionNumber) {
  return this.versions.find(v => v.version === versionNumber);
};

// Method to transition feature state
featureSchema.methods.transitionState = async function(newState) {
  const validTransitions = {
    'NEW': ['OPEN'],
    'OPEN': ['CLOSED'],
    'CLOSED': [] // Cannot transition from CLOSED
  };

  if (!validTransitions[this.state].includes(newState)) {
    throw new Error(`Invalid state transition from ${this.state} to ${newState}`);
  }

  this.state = newState;
  return this.save();
};

// Method to check if feature can be closed (all child requirements in final state)
featureSchema.methods.canBeClosed = async function() {
  const Requirement = require('./Requirement');
  const childRequirements = await Requirement.find({ feature: this._id });

  if (childRequirements.length === 0) {
    return { canClose: true, reason: null };
  }

  for (const requirement of childRequirements) {
    const currentVersion = requirement.versions[requirement.versions.length - 1];
    if (currentVersion.state !== 'Shipped/Deployed to Customer') {
      return {
        canClose: false,
        reason: `Requirement "${currentVersion.title}" is not in final state (current: ${currentVersion.state})`
      };
    }
  }

  return { canClose: true, reason: null };
};

// Method to add a release tag to a specific version
featureSchema.methods.addReleaseTag = function(tag, userId, versionNumber = null) {
  // Default to current version if no version specified
  const targetVersion = versionNumber || this.currentVersion;

  // Find the version
  const version = this.versions.find(v => v.version === targetVersion);
  if (!version) {
    throw new Error(`Version ${targetVersion} not found`);
  }

  // Check if tag already exists in this version
  const existingTag = version.releaseTags.find(t => t.tag === tag);
  if (existingTag) {
    // Make tag unique with timestamp
    tag = `${tag}-${Date.now()}`;
  }

  version.releaseTags.push({
    tag: tag,
    addedBy: userId,
    dateAdded: new Date()
  });

  return this.save();
};

// Method to remove a release tag from a specific version
featureSchema.methods.removeReleaseTag = function(tag, versionNumber = null) {
  // Default to current version if no version specified
  const targetVersion = versionNumber || this.currentVersion;

  // Find the version
  const version = this.versions.find(v => v.version === targetVersion);
  if (!version) {
    throw new Error(`Version ${targetVersion} not found`);
  }

  version.releaseTags = version.releaseTags.filter(t => t.tag !== tag);
  return this.save();
};

// Method to get all tags for a specific version
featureSchema.methods.getVersionTags = function(versionNumber = null) {
  const targetVersion = versionNumber || this.currentVersion;
  const version = this.versions.find(v => v.version === targetVersion);
  return version ? version.releaseTags : [];
};

// Update the updatedAt timestamp before saving
featureSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const Feature = mongoose.model('Feature', featureSchema);

module.exports = Feature;