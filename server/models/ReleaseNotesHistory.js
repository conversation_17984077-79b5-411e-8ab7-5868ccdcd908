const mongoose = require('mongoose');

const releaseNotesHistorySchema = new mongoose.Schema({
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },

  // Generated content
  content: {
    type: String,
    required: true,
    default: ''
  },

  generatedAt: {
    type: Date,
    default: Date.now
  },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Updated filters structure for version-specific tags
  filters: {
    tags: [{
      type: String
    }],
    labels: [{
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Label'
      },
      name: String
    }],
    states: [{
      type: String
    }],
    includeFeatures: {
      type: Boolean,
      default: true
    },
    includeRequirements: {
      type: Boolean,
      default: true
    },
    groupByFeature: {
      type: Boolean,
      default: true
    }
  },

  // Statistics about the generated release notes
  stats: {
    featuresIncluded: {
      type: Number,
      default: 0
    },
    requirementsIncluded: {
      type: Number,
      default: 0
    },
    versionsIncluded: {
      type: Number,
      default: 0
    },
    tagsUsed: [{
      type: String
    }]
  }
}, {
  timestamps: true
});

// Add indexes for performance
releaseNotesHistorySchema.index({ group: 1, project: 1, generatedAt: -1 });
releaseNotesHistorySchema.index({ generatedAt: -1 });

// Method to generate release notes content from data
releaseNotesHistorySchema.statics.generateContent = function(data, filters) {
  const { features, requirements, allFeatures } = data;
  // Use allFeatures for display if available, otherwise fall back to features
  const displayFeatures = allFeatures || features;
  let content = '';

  // Header
  content += `${filters.title || 'Release Notes'}\n`;
  content += `${'='.repeat((filters.title || 'Release Notes').length)}\n\n`;
  content += `Generated on: ${new Date().toLocaleString()}\n\n`;

  // Summary
  const featureCount = features.length;
  const requirementCount = requirements.length;
  // Only count current versions, not all historical versions
  const currentVersionsCount = featureCount + requirementCount;

  // Summary section
  content += `---------------\n`;
  content += `Summary\n\n`;

  // Only mention what's actually included
  const includedItems = [];
  if (featureCount > 0) {
    includedItems.push(`${featureCount} feature${featureCount !== 1 ? 's' : ''}`);
  }
  if (requirementCount > 0) {
    includedItems.push(`${requirementCount} requirement${requirementCount !== 1 ? 's' : ''}`);
  }

  if (includedItems.length > 0) {
    content += `This release includes ${includedItems.join(' and ')} (${currentVersionsCount} total items).\n`;
  } else {
    content += `No items match the selected filter criteria.\n`;
  }

  // Filter information
  const hasFilters = (filters.tags && filters.tags.length > 0) ||
                    (filters.labels && filters.labels.length > 0) ||
                    (filters.states && filters.states.length > 0);

  if (hasFilters) {
    content += `---------------\n\n`;
    content += `Filter Criteria\n\n`;
    if (filters.tags && filters.tags.length > 0) {
      content += `Release Tags: ${filters.tags.join(', ')}\n`;
    }
    if (filters.labels && filters.labels.length > 0) {
      content += `Labels: ${filters.labels.map(l => l.name || l).join(', ')}\n`;
    }
    if (filters.states && filters.states.length > 0) {
      content += `States: ${filters.states.join(', ')}\n`;
    }
    content += `---------------\n\n`;
  } else {
    content += `---------------\n\n`;
  }

  // Group by feature if requested
  if (filters.groupByFeature) {

    // Group requirements by their parent features
    const requirementsByFeature = new Map();
    const standaloneRequirements = [];

    requirements.forEach(req => {
      if (req.feature && req.feature._id) {
        const featureId = req.feature._id.toString();
        if (!requirementsByFeature.has(featureId)) {
          requirementsByFeature.set(featureId, []);
        }
        requirementsByFeature.get(featureId).push(req);
      } else {
        standaloneRequirements.push(req);
      }
    });

    // Process features that have matching requirements
    requirementsByFeature.forEach((childRequirements, featureId) => {
      // Find the feature details from displayFeatures
      const feature = displayFeatures.find(f => f._id.toString() === featureId);
      if (!feature) return;

      const currentVersion = feature.versions[feature.versions.length - 1];

      // Feature ID and title
      content += `Feature ${feature._id}\n`;
      content += `${currentVersion.title}\n\n`;

      // Show child requirements
      if (childRequirements.length > 0) {
        childRequirements.forEach(req => {
          const reqVersion = req.versions[req.versions.length - 1];

          // Requirement ID and title
          content += `Requirement ${req._id}\n`;
          content += `${reqVersion.title}\n`;

          // Clean description (remove HTML tags)
          if (reqVersion.description) {
            const cleanDescription = reqVersion.description.replace(/<[^>]*>/g, '').trim();
            if (cleanDescription) {
              content += `${cleanDescription}\n`;
            }
          }
          content += '\n';
        });
      }
    });

    // Handle standalone requirements
    if (standaloneRequirements.length > 0) {
      standaloneRequirements.forEach(req => {
        const currentVersion = req.versions[req.versions.length - 1];

        // Requirement ID and title
        content += `Requirement ${req._id}\n`;
        content += `${currentVersion.title}\n`;

        // Clean description (remove HTML tags)
        if (currentVersion.description) {
          const cleanDescription = currentVersion.description.replace(/<[^>]*>/g, '').trim();
          if (cleanDescription) {
            content += `${cleanDescription}\n`;
          }
        }
        content += '\n';
      });
    }
  } else {
    // Flat listing
    if (filters.includeFeatures && features.length > 0) {
      content += `Features\n`;
      content += `--------\n\n`;
      features.forEach(feature => {
        const currentVersion = feature.versions[feature.versions.length - 1];
        let title = currentVersion.title;
        if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
          title += ` [${currentVersion.releaseTags.map(t => t.tag).join(', ')}]`;
        }
        content += `• ${title}\n`;
        content += `  State: ${currentVersion.state}`;
        if (currentVersion.labels && currentVersion.labels.length > 0) {
          content += ` | Labels: ${currentVersion.labels.map(l => l.name || l).join(', ')}`;
        }
        content += ` | Version: ${feature.currentVersion}\n\n`;
      });
    }

    if (filters.includeRequirements && requirements.length > 0) {
      content += `Requirements\n`;
      content += `------------\n\n`;
      requirements.forEach(req => {
        const currentVersion = req.versions[req.versions.length - 1];
        let title = currentVersion.title;
        if (currentVersion.releaseTags && currentVersion.releaseTags.length > 0) {
          title += ` [${currentVersion.releaseTags.map(t => t.tag).join(', ')}]`;
        }
        content += `• ${title}\n`;
        content += `  State: ${currentVersion.state}`;
        if (currentVersion.labels && currentVersion.labels.length > 0) {
          content += ` | Labels: ${currentVersion.labels.map(l => l.name || l).join(', ')}`;
        }
        content += ` | Version: ${req.currentVersion}`;
        if (req.feature) {
          content += ` | Feature: ${req.feature.title}`;
        }
        content += '\n\n';
      });
    }
  }

  return content;
};

module.exports = mongoose.model('ReleaseNotesHistory', releaseNotesHistorySchema);
