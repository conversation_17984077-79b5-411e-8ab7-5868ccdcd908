const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const dotenv = require('dotenv');
const http = require('http');
const socketIo = require('socket.io');

// Load environment variables
const result = dotenv.config();
if (result.error) {
  console.error('Error loading .env file:', result.error);
} else {
  console.log('Environment variables loaded successfully');
  console.log('JWT_SECRET is set:', !!process.env.JWT_SECRET);
}

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/requirements-tool')
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.error('MongoDB connection error:', err));

// Import routes
const userRoutes = require('./routes/users');
const projectRoutes = require('./routes/projects');
const requirementRoutes = require('./routes/requirements');
const featureRoutes = require('./routes/features');
const labelRoutes = require('./routes/labels');
const authRoutes = require('./routes/auth');
const externalRoutes = require('./routes/external');
const releaseNotesRoutes = require('./routes/releaseNotes');
const auth = require('./middleware/auth');

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', auth, userRoutes);
app.use('/api/projects', auth, projectRoutes);
app.use('/api/requirements', auth, requirementRoutes);
app.use('/api/features', auth, featureRoutes);
app.use('/api/labels', auth, labelRoutes);
app.use('/api/external', auth, externalRoutes);
app.use('/api/release-notes', auth, releaseNotesRoutes);

// Track connected clients
const connectedClients = new Set();

// Socket.io connection handling
io.on('connection', (socket) => {
  const clientId = socket.id;
  
  // Check if this client is already connected
  if (connectedClients.has(clientId)) {
    console.log('Duplicate connection attempt from client:', clientId);
    socket.disconnect();
    return;
  }

  // Add client to connected set
  connectedClients.add(clientId);
  console.log('New client connected:', clientId);
  console.log('Total connected clients:', connectedClients.size);
  
  socket.on('joinRequirement', (requirementId) => {
    console.log(`Client ${clientId} joining requirement room: ${requirementId}`);
    socket.join(requirementId);
  });

  socket.on('leaveRequirement', (requirementId) => {
    console.log(`Client ${clientId} leaving requirement room: ${requirementId}`);
    socket.leave(requirementId);
  });

  socket.on('newComment', (data) => {
    console.log(`New comment from client ${clientId} for requirement: ${data.requirementId}`);
    io.to(data.requirementId).emit('commentAdded', data);
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', clientId);
    connectedClients.delete(clientId);
    console.log('Remaining connected clients:', connectedClients.size);
  });
});

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the Requirements Management Tool API' });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
}); 