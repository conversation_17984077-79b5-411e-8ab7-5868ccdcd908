const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup, addGroupToData } = require('../middleware/groupAuth');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const Project = require('../models/Project');
const Label = require('../models/Label');
const ReleaseNotesHistory = require('../models/ReleaseNotesHistory');

// Debug logging middleware for release notes routes
router.use((req, res, next) => {
  console.log('Release Notes route hit:', req.method, req.path);
  next();
});

// Get available tags and labels for a project (for filter options)
router.get('/projects/:projectId/document-data', auth, addGroupContext, async (req, res) => {
  try {
    // Build base filter for group access
    let baseFilter = { project: req.params.projectId };
    if (!req.isSuperUser) {
      baseFilter.group = req.userGroup._id;
    }

    // Get all features and requirements for this project
    const features = await Feature.find(baseFilter);
    const requirements = await Requirement.find(baseFilter);

    // Extract unique tags from all versions
    const allTags = new Set();

    features.forEach(feature => {
      feature.versions.forEach(version => {
        if (version.releaseTags) {
          version.releaseTags.forEach(tagObj => {
            allTags.add(tagObj.tag);
          });
        }
      });
    });

    requirements.forEach(requirement => {
      requirement.versions.forEach(version => {
        if (version.releaseTags) {
          version.releaseTags.forEach(tagObj => {
            allTags.add(tagObj.tag);
          });
        }
      });
    });

    // Get all labels for this group (labels are group-scoped, not project-scoped)
    let labelFilter = {};
    if (!req.isSuperUser) {
      labelFilter.group = req.userGroup._id;
    }
    const labels = await Label.find(labelFilter);

    res.json({
      tags: Array.from(allTags).sort(),
      labels: labels
    });
  } catch (err) {
    console.error('Error fetching release data options:', err);
    res.status(500).send('Server Error');
  }
});

// Generate documents for a project with filtering
router.post('/projects/:projectId/documents', auth, addGroupContext, async (req, res) => {
  try {
    const { filters, title, save, documentType = 'release_notes' } = req.body;

    if (!filters) {
      return res.status(400).json({ message: 'Filters are required' });
    }

    // Build base filter for group access
    let baseFilter = { project: req.params.projectId };
    if (!req.isSuperUser) {
      baseFilter.group = req.userGroup._id;
    }

    // Helper function to get the appropriate version based on filter criteria
    const getElementVersion = (element, versionFilter, targetState) => {
      if (versionFilter === 'latest') {
        return element.versions[element.versions.length - 1];
      } else if (versionFilter === 'latest_with_state' && targetState) {
        // Find latest version with targetState
        const versionsWithState = element.versions
          .filter(v => v.state === targetState)
          .sort((a, b) => b.version - a.version);
        return versionsWithState[0] || null;
      }
      // Default to latest if no valid filter
      return element.versions[element.versions.length - 1];
    };

    // Get all features and requirements for this project
    let featuresData = [];
    let requirementsData = [];

    if (filters.includeFeatures) {
      featuresData = await Feature.find(baseFilter)
        .populate('createdBy', 'username firstName lastName')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color')
        .sort({ createdAt: -1 });
    }

    if (filters.includeRequirements) {
      requirementsData = await Requirement.find(baseFilter)
        .populate('createdBy', 'username firstName lastName')
        .populate('feature', 'title')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color')
        .sort({ createdAt: -1 });
    }

    // Apply filters to get matching items with version-aware filtering
    const filteredFeatures = featuresData.filter(feature => {
      // Get the appropriate version for filtering based on version filter
      const targetVersion = getElementVersion(feature, filters.versionFilter, filters.targetState);
      if (!targetVersion) return false; // Skip if no version matches criteria

      // Check tags (if any tags specified, at least one must match)
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = targetVersion.releaseTags &&
          targetVersion.releaseTags.some(tagObj => filters.tags.includes(tagObj.tag));
        if (!hasMatchingTag) return false;
      }

      // Check labels (if any labels specified, at least one must match)
      if (filters.labels && filters.labels.length > 0) {
        const labelIds = filters.labels.map(label => label._id || label);
        const hasMatchingLabel = targetVersion.labels &&
          targetVersion.labels.some(label => labelIds.includes(label.toString()));
        if (!hasMatchingLabel) return false;
      }

      // Check states (if any states specified, must match one of them)
      if (filters.states && filters.states.length > 0) {
        if (!filters.states.includes(targetVersion.state)) return false;
      }

      // Store the version used for this feature for document labeling
      feature.versionUsed = targetVersion.version;

      return true;
    });

    const filteredRequirements = requirementsData.filter(requirement => {
      // Get the appropriate version for filtering based on version filter
      const targetVersion = getElementVersion(requirement, filters.versionFilter, filters.targetState);
      if (!targetVersion) return false; // Skip if no version matches criteria

      // Check tags (if any tags specified, at least one must match)
      if (filters.tags && filters.tags.length > 0) {
        const hasMatchingTag = targetVersion.releaseTags &&
          targetVersion.releaseTags.some(tagObj => filters.tags.includes(tagObj.tag));
        if (!hasMatchingTag) return false;
      }

      // Check labels (if any labels specified, at least one must match)
      if (filters.labels && filters.labels.length > 0) {
        const labelIds = filters.labels.map(label => label._id || label);
        const hasMatchingLabel = targetVersion.labels &&
          targetVersion.labels.some(label => labelIds.includes(label.toString()));
        if (!hasMatchingLabel) return false;
      }

      // Check states (if any states specified, must match one of them)
      if (filters.states && filters.states.length > 0) {
        if (!filters.states.includes(targetVersion.state)) return false;
      }

      // Store the version used for this requirement for document labeling
      requirement.versionUsed = targetVersion.version;

      return true;
    });

    // For requirements that have parent features, include those features for context
    // even if the features themselves don't match the filter criteria
    const parentFeatureIds = new Set();
    filteredRequirements.forEach(req => {
      if (req.feature && req.feature._id) {
        parentFeatureIds.add(req.feature._id.toString());
      }
    });

    // Get parent features that aren't already in filteredFeatures
    const parentFeatures = await Feature.find({
      ...baseFilter,
      _id: { $in: Array.from(parentFeatureIds) }
    })
    .populate('createdBy', 'username firstName lastName')
    .populate('versions.releaseTags.addedBy', 'username firstName lastName')
    .populate('versions.labels', 'name color');

    // Combine filtered features with parent features (avoid duplicates)
    const allRelevantFeatures = [...filteredFeatures];
    parentFeatures.forEach(parentFeature => {
      if (!filteredFeatures.some(f => f._id.toString() === parentFeature._id.toString())) {
        allRelevantFeatures.push(parentFeature);
      }
    });

    // Generate release notes content
    const releaseData = {
      features: filteredFeatures, // Only filtered features for counting
      requirements: filteredRequirements,
      allFeatures: allRelevantFeatures // All relevant features for display
    };

    const content = ReleaseNotesHistory.generateContent(releaseData, { ...filters, title });

    // Add document labels to all included elements only when saving
    if (save && title && title.trim()) {
      const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const documentLabelData = {
        documentId,
        documentType,
        generatedAt: new Date(),
        parameters: {
          tags: filters.tags || [],
          labels: filters.labels || [],
          states: filters.states || [],
          features: filters.features || [],
          versionFilter: filters.versionFilter || 'latest',
          targetState: filters.targetState || null,
          includeFeatures: filters.includeFeatures || false,
          includeRequirements: filters.includeRequirements || false
        },
        title: title.trim()
      };

      // Add document labels to specific versions of features
      for (const feature of filteredFeatures) {
        await Feature.findOneAndUpdate(
          {
            _id: feature._id,
            'versions.version': feature.versionUsed
          },
          {
            $push: {
              'versions.$.documentLabels': {
                documentId,
                documentType,
                generatedAt: new Date(),
                title: title.trim()
              }
            }
          }
        );
      }

      // Add document labels to specific versions of requirements
      for (const requirement of filteredRequirements) {
        await Requirement.findOneAndUpdate(
          {
            _id: requirement._id,
            'versions.version': requirement.versionUsed
          },
          {
            $push: {
              'versions.$.documentLabels': {
                documentId,
                documentType,
                generatedAt: new Date(),
                title: title.trim()
              }
            }
          }
        );
      }

      // Calculate statistics with document ID
      const stats = {
        featuresIncluded: filteredFeatures.length,
        requirementsIncluded: filteredRequirements.length,
        versionsIncluded: filteredFeatures.length + filteredRequirements.length,
        tagsUsed: filters.tags || [],
        documentId: documentId
      };

      // Save to history
      const releaseNotesData = addGroupToData(req, {
        project: req.params.projectId,
        title: title.trim(),
        content: content,
        generatedBy: req.user.userId,
        filters: filters,
        stats: stats
      });

      const releaseNotes = new ReleaseNotesHistory(releaseNotesData);
      await releaseNotes.save();
    }

    // Calculate statistics (without document ID if not saving)
    const stats = {
      featuresIncluded: filteredFeatures.length,
      requirementsIncluded: filteredRequirements.length,
      versionsIncluded: filteredFeatures.length + filteredRequirements.length,
      tagsUsed: filters.tags || []
    };

    res.json({
      data: releaseData,
      releaseNotes: content,
      stats: stats
    });
  } catch (err) {
    console.error('Error generating release notes:', err);
    res.status(500).send('Server Error');
  }
});



// Get historical release notes for a project
router.get('/projects/:projectId/release-notes', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    let filter = { project: req.params.projectId };
    
    // Add group filter for non-super users
    if (!req.isSuperUser) {
      filter.group = req.userGroup._id;
    }

    const releaseNotes = await ReleaseNotesHistory.find(filter)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description')
      .sort({ generatedAt: -1 });

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching release notes history:', err);
    res.status(500).send('Server Error');
  }
});

// Get specific release notes data
router.get('/projects/:projectId/release-notes/:releaseNotesId', auth, addGroupContext, async (req, res) => {
  try {
    const releaseNotes = await ReleaseNotesHistory.findById(req.params.releaseNotesId)
      .populate('generatedBy', 'username firstName lastName')
      .populate('project', 'name description');

    if (!releaseNotes) {
      return res.status(404).json({ message: 'Release notes not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && releaseNotes.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json(releaseNotes);
  } catch (err) {
    console.error('Error fetching specific release notes:', err);
    res.status(500).send('Server Error');
  }
});

// Recreate historical document from document ID
router.get('/documents/:documentId/recreate', auth, addGroupContext, async (req, res) => {
  try {
    const { documentId } = req.params;
    console.log('Attempting to recreate document:', documentId);

    // Find any element with this documentId to get the original parameters
    const featureWithLabel = await Feature.findOne({
      'versions.documentLabels.documentId': documentId
    });

    const requirementWithLabel = await Requirement.findOne({
      'versions.documentLabels.documentId': documentId
    });

    const elementWithLabel = featureWithLabel || requirementWithLabel;
    console.log('Found element with label:', !!elementWithLabel);

    if (!elementWithLabel) {
      console.log('No element found with document ID:', documentId);
      return res.status(404).json({ message: 'Document not found' });
    }

    // Get the document label data from the version
    let documentLabel = null;
    let elementVersion = null;

    for (const version of elementWithLabel.versions) {
      const label = version.documentLabels.find(label => label.documentId === documentId);
      if (label) {
        documentLabel = label;
        elementVersion = version.version;
        break;
      }
    }

    if (!documentLabel) {
      return res.status(404).json({ message: 'Document label not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && elementWithLabel.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Get the project ID from the element
    const projectId = elementWithLabel.project.toString();

    // For now, we'll use basic parameters since we removed the complex parameters structure
    // In a real implementation, you might want to store these parameters separately
    const originalParams = {
      includeFeatures: true,
      includeRequirements: true,
      groupByFeature: true,
      tags: [],
      labels: [],
      states: [],
      versionFilter: 'latest'
    };

    // Get all elements that were part of this document
    const [featuresInDoc, requirementsInDoc] = await Promise.all([
      Feature.find({
        'versions.documentLabels.documentId': documentId
      }).populate('createdBy', 'username firstName lastName')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color'),

      Requirement.find({
        'versions.documentLabels.documentId': documentId
      }).populate('createdBy', 'username firstName lastName')
        .populate('feature', 'title')
        .populate('versions.releaseTags.addedBy', 'username firstName lastName')
        .populate('versions.labels', 'name color')
    ]);

    // Get the specific versions that were used in the original document
    const historicalFeatures = featuresInDoc.map(feature => {
      // Find the version that has this document label
      let versionUsed = null;
      for (const version of feature.versions) {
        if (version.documentLabels.some(label => label.documentId === documentId)) {
          versionUsed = version.version;
          break;
        }
      }
      const historicalVersion = feature.versions.find(v => v.version === versionUsed);
      return {
        ...feature.toObject(),
        currentVersion: historicalVersion || feature.versions[feature.versions.length - 1],
        versionUsed: versionUsed
      };
    });

    const historicalRequirements = requirementsInDoc.map(requirement => {
      // Find the version that has this document label
      let versionUsed = null;
      for (const version of requirement.versions) {
        if (version.documentLabels.some(label => label.documentId === documentId)) {
          versionUsed = version.version;
          break;
        }
      }
      const historicalVersion = requirement.versions.find(v => v.version === versionUsed);
      return {
        ...requirement.toObject(),
        currentVersion: historicalVersion || requirement.versions[requirement.versions.length - 1],
        versionUsed: versionUsed
      };
    });

    // Generate the historical document content
    const releaseData = {
      features: historicalFeatures,
      requirements: historicalRequirements,
      allFeatures: historicalFeatures
    };

    const content = ReleaseNotesHistory.generateContent(releaseData, {
      ...originalParams,
      title: documentLabel.title
    });

    res.json({
      data: releaseData,
      releaseNotes: content,
      originalParameters: originalParams,
      documentInfo: {
        documentId: documentId,
        title: documentLabel.title,
        generatedAt: documentLabel.generatedAt,
        documentType: documentLabel.documentType
      },
      stats: {
        featuresIncluded: historicalFeatures.length,
        requirementsIncluded: historicalRequirements.length,
        versionsIncluded: historicalFeatures.length + historicalRequirements.length,
        isHistorical: true
      }
    });
  } catch (err) {
    console.error('Error recreating historical document:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
